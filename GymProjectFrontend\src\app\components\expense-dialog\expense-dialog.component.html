<div class="modern-dialog zoom-in expense-dialog">
  <div class="modern-card-header">
    <h2>
      <i class="fas fa-receipt modern-header-icon"></i>
      {{ isEditMode ? 'Gider Düzenle' : '<PERSON><PERSON>' }}
    </h2>
    <button class="modern-btn-icon" (click)="closeDialog()">
      <i class="fas fa-times"></i>
    </button>
  </div>

  <form [formGroup]="expenseForm" (ngSubmit)="saveExpense()">
    <div class="modern-card-body">

      <!-- Gider Türü (Dropdown) -->
      <div class="form-group">
        <label class="form-label">
          <i class="fas fa-tag form-label-icon"></i>
          Gider Türü
        </label>
        <select class="modern-form-control" formControlName="expenseType">
          <option value="" disabled>Gider türü seçin</option>
          <option *ngFor="let type of expenseTypes" [value]="type">
            {{ type }}
          </option>
        </select>
        <div class="form-error" *ngIf="expenseType?.hasError('required') && expenseType?.touched">
          <i class="fas fa-exclamation-circle"></i>
          Gider türü seçmek zorunludur.
        </div>
        <div class="form-error" *ngIf="expenseType?.hasError('maxlength') && expenseType?.touched">
          <i class="fas fa-exclamation-circle"></i>
          Gider türü en fazla 100 karakter olabilir.
        </div>
      </div>

      <div class="form-row">
        <!-- Tutar -->
        <div class="form-col">
          <div class="form-group">
            <label class="form-label">
              <i class="fas fa-lira-sign form-label-icon"></i>
              Tutar (TL)
            </label>
            <div class="input-with-prefix">
              <span class="input-prefix">₺</span>
              <input
                type="number"
                class="modern-form-control with-prefix"
                formControlName="amount"
                placeholder="0.00"
                step="0.01"
                min="0"
              >
            </div>
            <div class="form-error" *ngIf="amount?.hasError('required') && amount?.touched">
              <i class="fas fa-exclamation-circle"></i>
              Tutar alanı zorunludur.
            </div>
            <div class="form-error" *ngIf="amount?.hasError('min') && amount?.touched">
              <i class="fas fa-exclamation-circle"></i>
              Tutar 0'dan büyük olmalıdır.
            </div>
          </div>
        </div>

        <!-- Gider Tarihi -->
        <div class="form-col">
          <div class="form-group">
            <label class="form-label">
              <i class="fas fa-calendar-alt form-label-icon"></i>
              Gider Tarihi
            </label>
            <input
              type="date"
              class="modern-form-control"
              formControlName="expenseDate"
            >
            <div class="form-error" *ngIf="expenseDate?.hasError('required') && expenseDate?.touched">
              <i class="fas fa-exclamation-circle"></i>
              Gider tarihi zorunludur.
            </div>
          </div>
        </div>
      </div>

      <!-- Açıklama -->
      <div class="form-group">
        <label class="form-label">
          <i class="fas fa-comment-alt form-label-icon"></i>
          Açıklama (Opsiyonel)
        </label>
        <textarea
          class="modern-form-control"
          formControlName="description"
          rows="3"
          placeholder="Giderin açıklamasını girin (örn: Elektrik Faturası)"
        ></textarea>
        <div class="form-hint">
          <i class="fas fa-info-circle"></i>
          Giderin açıklamasını girin (örn: Elektrik Faturası)
        </div>
        <div class="form-error" *ngIf="description?.hasError('maxlength') && description?.touched">
          <i class="fas fa-exclamation-circle"></i>
          Açıklama en fazla 500 karakter olabilir.
        </div>
      </div>

    </div>

    <div class="modern-card-footer">
      <button
        type="button"
        class="expense-dialog-btn expense-dialog-btn-cancel"
        (click)="closeDialog()">
        <i class="fas fa-times"></i> İptal
      </button>
      <button
        type="submit"
        class="expense-dialog-btn expense-dialog-btn-primary"
        [disabled]="isLoading || expenseForm.invalid">
        <span *ngIf="!isLoading">
          <i class="fas {{ isEditMode ? 'fa-edit' : 'fa-plus' }}"></i> {{ isEditMode ? 'Güncelle' : 'Kaydet' }}
        </span>
        <span *ngIf="isLoading">
          <i class="fas fa-spinner fa-spin"></i> Kaydediliyor...
        </span>
      </button>
    </div>
  </form>
</div>