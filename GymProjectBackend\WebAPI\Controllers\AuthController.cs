﻿﻿using Business.Abstract;
using Core.Entities.Concrete;
using Entities.DTOs;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel.DataAnnotations;
using System.Security.Claims;
using Microsoft.AspNetCore.RateLimiting; // Rate Limiting için eklendi

[Route("api/[controller]")]
[ApiController]
public class AuthController : Controller
{
    private IAuthService _authService;

    public AuthController(IAuthService authService)
    {
        _authService = authService;
    }

    [HttpPost("register")]
    public ActionResult Register([FromBody] RegisterRequestModel request)
    {
        var userExists = _authService.UserExists(request.RegisterDto.Email);
        if (!userExists.Success)
        {
            return BadRequest(new { success = false, message = userExists.Message });
        }

        var registerResult = _authService.Register(request.RegisterDto, request.RegisterDto.Password);
        if (!registerResult.Success)
        {
            return BadRequest(new { success = false, message = registerResult.Message });
        }

        var result = _authService.CreateAccessToken(registerResult.Data, request.DeviceInfo);
        if (result.Success)
        {
            return Ok(new
            {
                success = true,
                message = result.Message,
                data = new
                {
                    token = result.Data.Token,
                    refreshToken = result.Data.RefreshToken,
                    expiration = result.Data.Expiration
                }
            });
        }

        return BadRequest(new { success = false, message = result.Message });
    }

    [HttpPost("register-member")]
    public ActionResult RegisterMember([FromBody] MemberRegisterRequestModel request)
    {
        var userExists = _authService.UserExists(request.RegisterDto.Email);
        if (!userExists.Success)
        {
            return BadRequest(new { success = false, message = userExists.Message });
        }

        var registerResult = _authService.RegisterMember(request.RegisterDto, request.RegisterDto.Password);
        if (!registerResult.Success)
        {
            return BadRequest(new { success = false, message = registerResult.Message });
        }

        var result = _authService.CreateAccessToken(registerResult.Data, request.DeviceInfo);
        if (result.Success)
        {
            return Ok(new
            {
                success = true,
                message = result.Message,
                data = new
                {
                    token = result.Data.Token,
                    refreshToken = result.Data.RefreshToken,
                    expiration = result.Data.Expiration
                }
            });
        }

        return BadRequest(new { success = false, message = result.Message });
    }

    [HttpPost("login")]
    public ActionResult Login([FromBody] LoginRequestModel request)
    {
        var userToLogin = _authService.Login(request.LoginDto, request.DeviceInfo);
        if (!userToLogin.Success)
        {
            return BadRequest(new { success = false, message = userToLogin.Message });
        }


        // Şifre değiştirme zorunluluğu kontrolü
        bool requirePasswordChange = false;
        if (userToLogin.Message == "PasswordChangeRequired")
        {
            requirePasswordChange = true;
        }

        var result = _authService.CreateAccessToken(userToLogin.Data, request.DeviceInfo);
        if (result.Success)
        {
            return Ok(new
            {
                success = true,
                message = result.Message,
                requirePasswordChange = requirePasswordChange,
                data = new
                {
                    token = result.Data.Token,
                    refreshToken = result.Data.RefreshToken,
                    expiration = result.Data.Expiration
                }
            });
        }

        return BadRequest(new { success = false, message = result.Message });
    }

    [HttpPost("refresh-token")]
    public ActionResult RefreshToken([FromBody] RefreshTokenRequest request)
    {
        var ipAddress = HttpContext.Connection.RemoteIpAddress?.ToString();
        var result = _authService.CreateAccessTokenWithRefreshToken(request.RefreshToken, ipAddress, request.DeviceInfo);

        if (result.Success)
        {
            return Ok(new
            {
                success = true,
                message = result.Message,
                data = new
                {
                    token = result.Data.Token,
                    refreshToken = result.Data.RefreshToken,
                    expiration = result.Data.Expiration
                }
            });
        }

        return BadRequest(new { success = false, message = result.Message });
    }

    [HttpPost("logout")]
    [Authorize]
    public ActionResult Logout()
    {
        var refreshToken = Request.Headers["X-Refresh-Token"].ToString();
        if (string.IsNullOrEmpty(refreshToken))
        {
            return BadRequest(new { success = false, message = "Refresh token not provided" });
        }

        var result = _authService.RevokeRefreshToken(refreshToken);
        return result.Success ? Ok(result) : BadRequest(result);
    }

    [HttpPost("revoke-device")]
    [Authorize]
    public ActionResult RevokeDevice([FromBody] RevokeDeviceRequest request)
    {
        var result = _authService.RevokeDevice(request.DeviceId);
        return result.Success ? Ok(result) : BadRequest(result);
    }

    [HttpPost("revoke-all-devices")]
    [Authorize]
    public ActionResult RevokeAllDevices()
    {
        var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier).Value);
        var result = _authService.RevokeAllDevices(userId);
        return result.Success ? Ok(result) : BadRequest(result);
    }

    [HttpGet("devices")]
    [Authorize]
    public ActionResult GetUserDevices()
    {
        var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier).Value);
        var result = _authService.GetUserDevices(userId);
        return result.Success ? Ok(result) : BadRequest(result);
    }

    [HttpPost("change-company")]
    [Authorize]
    public ActionResult ChangeCompany([FromBody] ChangeCompanyRequest request)
    {
        var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier).Value);
        var result = _authService.ChangeCompany(userId, request.CompanyId, request.DeviceInfo);

        if (result.Success)
        {
            return Ok(new
            {
                success = true,
                message = result.Message,
                data = new
                {
                    token = result.Data.Token,
                    refreshToken = result.Data.RefreshToken,
                    expiration = result.Data.Expiration
                }
            });
        }

        return BadRequest(new { success = false, message = result.Message });
    }

    [HttpPost("change-password")]
    [Authorize]
    public ActionResult ChangePassword([FromBody] ChangePasswordRequest request)
    {
        var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier).Value);
        var result = _authService.ChangePassword(userId, request.CurrentPassword, request.NewPassword);

        if (result.Success)
        {
            return Ok(new { success = true, message = result.Message });
        }

        return BadRequest(new { success = false, message = result.Message });
    }

    [HttpGet("check-password-change-required")]
    [Authorize]
    public ActionResult CheckPasswordChangeRequired()
    {
        var userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier).Value);
        var result = _authService.CheckPasswordChangeRequired(userId);

        if (result.Success)
        {
            return Ok(new { success = true, requirePasswordChange = result.Data });
        }

        return BadRequest(new { success = false, message = result.Message });
    }

    [HttpGet("check-login-ban")]
    public ActionResult CheckLoginBan([FromQuery] string deviceInfo)
    {
        var ipAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "unknown";
        var userAgent = HttpContext.Request.Headers["User-Agent"].ToString() ?? "unknown";

        // Device fingerprint oluştur
        var deviceFingerprint = _authService.GenerateDeviceFingerprint(ipAddress, userAgent, deviceInfo);

        var result = _authService.GetRemainingLoginBanTime(ipAddress, deviceFingerprint);

        return Ok(new {
            success = true,
            remainingMinutes = result.Data,
            isBanned = result.Data > 0
        });
    }

    [HttpGet("check-register-ban")]
    public ActionResult CheckRegisterBan()
    {
        var ipAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "unknown";

        var result = _authService.GetRemainingRegisterBanTime(ipAddress);

        return Ok(new {
            success = true,
            remainingMinutes = result.Data,
            isBanned = result.Data > 0
        });
    }

    public class RegisterRequestModel
    {
        public UserForRegisterDto RegisterDto { get; set; }
        public string DeviceInfo { get; set; }
    }

    public class LoginRequestModel
    {
        public UserForLoginDto LoginDto { get; set; }
        public string DeviceInfo { get; set; }
    }

    public class RefreshTokenRequest
    {
        [Required]
        public string RefreshToken { get; set; }
        public string DeviceInfo { get; set; }
    }

    public class ChangeCompanyRequest
    {
        [Required]
        public int CompanyId { get; set; }
        public string DeviceInfo { get; set; }
    }

    public class MemberRegisterRequestModel
    {
        public MemberForRegisterDto RegisterDto { get; set; }
        public string DeviceInfo { get; set; }
    }

    public class ChangePasswordRequest
    {
        [Required]
        public string CurrentPassword { get; set; }

        [Required]
        [MinLength(6, ErrorMessage = "Şifre en az 6 karakter olmalıdır.")]
        public string NewPassword { get; set; }
    }
}
