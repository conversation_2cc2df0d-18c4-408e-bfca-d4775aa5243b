
export default {
  bootstrap: () => import('./main.server.mjs').then(m => m.default),
  inlineCriticalCss: true,
  baseHref: '/',
  locale: undefined,
  routes: undefined,
  entryPointToBrowserMapping: {},
  assets: {
    'index.csr.html': {size: 41548, hash: '483a75c970178b2648734f55b3ac93bfa38412cf4e1526b2667822820874d1b0', text: () => import('./assets-chunks/index_csr_html.mjs').then(m => m.default)},
    'index.server.html': {size: 21716, hash: '4ecc421fd56180cacbf894d21aa170dc9e099b7a42ddf379568269006a143a7c', text: () => import('./assets-chunks/index_server_html.mjs').then(m => m.default)},
    'styles-UJ27JA5M.css': {size: 298495, hash: 'KuJzfxHS510', text: () => import('./assets-chunks/styles-UJ27JA5M_css.mjs').then(m => m.default)}
  },
};
