/* Member Remaining Day Component Styles */

/* Loading Spinner */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  backdrop-filter: blur(3px);
}

.spinner-container {
  text-align: center;
}

/* Content Blur */
.content-blur {
  filter: blur(3px);
  pointer-events: none;
}

/* Fade In Animation */
.fade-in {
  animation: fadeIn 0.5s ease-out;
}

/* Modern Stats Cards */
.modern-stats-card {
  border-radius: var(--border-radius-lg);
  padding: 1.5rem;
  height: 100%;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  margin-bottom: 1rem;
  position: relative;
  overflow: hidden;
}

.modern-stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: inherit;
  opacity: 0.9;
  z-index: 1;
}

.modern-stats-card > * {
  position: relative;
  z-index: 2;
}

.modern-stats-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.modern-stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  margin-right: 1rem;
  background-color: rgba(255, 255, 255, 0.2);
  flex-shrink: 0;
}

.modern-stats-info {
  flex-grow: 1;
}

.modern-stats-value {
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
  color: white;
}

.modern-stats-label {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: white;
}

.modern-stats-subtext {
  font-size: 0.875rem;
  opacity: 0.8;
  color: white;
}

/* Background Gradients */
.bg-primary-gradient {
  background: linear-gradient(135deg, #4361ee 0%, #3a0ca3 100%);
  color: white;
}

.bg-warning-gradient {
  background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
  color: white;
}

.bg-danger-gradient {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
  color: white;
}

.bg-info-gradient {
  background: linear-gradient(135deg, #0dcaf0 0%, #0097b2 100%);
  color: white;
}

/* Header Styles */
.header-title h5 {
  color: var(--text-primary);
  font-weight: 600;
}

.header-title p {
  font-size: 0.875rem;
  color: var(--text-muted);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* Modern Search Box */
.search-container {
  position: relative;
}

.modern-search-box {
  position: relative;
  display: flex;
  align-items: center;
  width: 300px;
}

.search-icon {
  position: absolute;
  left: 12px;
  color: var(--text-muted);
  z-index: 3;
  font-size: 0.875rem;
}

.modern-form-control {
  width: 100%;
  padding: 0.75rem 2.5rem 0.75rem 2.5rem;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  background-color: var(--input-bg);
  color: var(--input-text);
  font-size: 0.875rem;
  transition: all 0.3s ease;
}

.modern-form-control:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.1);
}

.clear-search {
  position: absolute;
  right: 8px;
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: 4px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  z-index: 3;
  transition: all 0.2s ease;
}

.clear-search:hover {
  background-color: var(--danger-light);
  color: var(--danger);
}

/* Member Avatar */
.member-info {
  display: flex;
  align-items: center;
}

.member-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  margin-right: 0.75rem;
  flex-shrink: 0;
}

/* Status Badge */
.status-badge {
  display: inline-block;
  padding: 0.35rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 600;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 50rem;
}

.status-danger {
  background-color: var(--danger-light);
  color: var(--danger);
}

.status-warning {
  background-color: var(--warning-light);
  color: var(--warning);
}

.status-success {
  background-color: var(--success-light);
  color: var(--success);
}

/* Table Header */
.table-header {
  padding-bottom: 1rem;
  border-bottom: 2px solid var(--border-color);
}

.member-count {
  font-size: 0.875rem;
  color: var(--text-muted);
  font-weight: 500;
}

.sort-toggle-btn {
  display: flex;
  align-items: center;
  background: none;
  border: 2px solid var(--border-color);
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius-lg);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.875rem;
  font-weight: 500;
}

.sort-toggle-btn:hover {
  border-color: var(--primary);
  color: var(--primary);
  background-color: var(--primary-light);
}

/* Modern Table */
.modern-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  margin-top: 1rem;
}

.modern-table th {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.5px;
  padding: 1rem;
  border-bottom: 2px solid var(--border-color);
  position: sticky;
  top: 0;
  z-index: 10;
}

.modern-table td {
  padding: 1rem;
  vertical-align: middle;
  border-bottom: 1px solid var(--border-color);
}

.modern-table tbody tr {
  transition: all 0.3s ease;
}

.modern-table tbody tr:hover {
  background-color: var(--primary-light);
  transform: translateX(5px);
}

/* Table Row Classes */
.table-row-urgent {
  border-left: 4px solid var(--danger);
  background-color: rgba(220, 53, 69, 0.05);
}

.table-row-warning {
  border-left: 4px solid var(--warning);
  background-color: rgba(255, 193, 7, 0.05);
}

.table-row-normal {
  border-left: 4px solid var(--success);
  background-color: rgba(46, 204, 113, 0.05);
}

/* Table Cell Content */
.member-info {
  display: flex;
  align-items: center;
}

.member-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  margin-right: 0.75rem;
  flex-shrink: 0;
  font-size: 0.875rem;
}

.member-details {
  flex-grow: 1;
}

.member-name {
  font-size: 0.95rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0;
}

.phone-number {
  font-size: 0.875rem;
  color: var(--text-primary);
  font-weight: 500;
}

.branch-name {
  font-size: 0.875rem;
  color: var(--text-muted);
  font-weight: 500;
}

.remaining-days-badge {
  display: inline-block;
  padding: 0.375rem 0.75rem;
  border-radius: var(--border-radius-pill);
  font-size: 0.875rem;
  font-weight: 600;
  background-color: var(--bg-secondary);
  text-align: center;
}

/* Status Badge Styles */
.status-badge {
  display: inline-block;
  padding: 0.35rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 600;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 50rem;
}

.status-danger {
  background-color: var(--danger-light);
  color: var(--danger);
}

.status-warning {
  background-color: var(--warning-light);
  color: var(--warning);
}

.status-success {
  background-color: var(--success-light);
  color: var(--success);
}

/* Empty State */
.empty-state {
  padding: 4rem 2rem;
  text-align: center;
  color: var(--text-secondary);
}

.empty-icon {
  margin-bottom: 1.5rem;
  color: var(--text-muted);
  opacity: 0.6;
}

.empty-state h4 {
  color: var(--text-primary);
  font-weight: 600;
  margin-bottom: 1rem;
}

.empty-state p {
  color: var(--text-muted);
  font-size: 0.95rem;
  margin-bottom: 1.5rem;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.member-card {
  animation: slideInUp 0.6s ease-out;
}

.member-card:nth-child(1) { animation-delay: 0.1s; }
.member-card:nth-child(2) { animation-delay: 0.2s; }
.member-card:nth-child(3) { animation-delay: 0.3s; }
.member-card:nth-child(4) { animation-delay: 0.4s; }
.member-card:nth-child(5) { animation-delay: 0.5s; }
.member-card:nth-child(6) { animation-delay: 0.6s; }

/* Dark Mode Support */
[data-theme="dark"] .loading-overlay {
  background-color: rgba(18, 18, 18, 0.8);
}

[data-theme="dark"] .modern-table th {
  background-color: var(--bg-tertiary);
}

[data-theme="dark"] .modern-table tbody tr:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .table-row-urgent {
  background-color: rgba(220, 53, 69, 0.1);
}

[data-theme="dark"] .table-row-warning {
  background-color: rgba(255, 193, 7, 0.1);
}

[data-theme="dark"] .table-row-normal {
  background-color: rgba(46, 204, 113, 0.1);
}

/* Responsive Adjustments */
@media (max-width: 991.98px) {
  .modern-card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .header-actions {
    width: 100%;
  }

  .modern-search-box {
    width: 100%;
  }

  .table-header .d-flex {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
}

@media (max-width: 767.98px) {
  .modern-table th,
  .modern-table td {
    padding: 0.75rem 0.5rem;
  }

  .modern-table th {
    font-size: 0.7rem;
  }

  .member-avatar {
    width: 35px;
    height: 35px;
    font-size: 0.75rem;
    margin-right: 0.5rem;
  }

  .member-name {
    font-size: 0.875rem;
  }

  .phone-number,
  .branch-name {
    font-size: 0.8rem;
  }

  .remaining-days-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
  }

  .modern-btn-sm {
    font-size: 0.75rem;
    padding: 0.375rem 0.5rem;
  }

  .empty-state {
    padding: 3rem 1rem;
  }

  .empty-icon {
    font-size: 3rem !important;
  }
}

@media (max-width: 575.98px) {
  .modern-table {
    font-size: 0.875rem;
  }

  .modern-table th,
  .modern-table td {
    padding: 0.5rem 0.25rem;
  }

  .member-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .member-avatar {
    width: 30px;
    height: 30px;
    font-size: 0.7rem;
    margin-right: 0;
  }



  .modern-btn-sm {
    font-size: 0.7rem;
    padding: 0.25rem 0.375rem;
  }

  /* Hide some columns on very small screens */
  .modern-table th:nth-child(3),
  .modern-table td:nth-child(3) {
    display: none;
  }
}
