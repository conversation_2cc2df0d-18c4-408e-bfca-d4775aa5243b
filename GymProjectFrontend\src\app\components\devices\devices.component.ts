// components/devices/devices.component.ts
import { Component, OnInit } from '@angular/core';
import { AuthService } from '../../services/auth.service';
import { UserDevice } from '../../models/userDevice';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-devices',
  templateUrl: './devices.component.html',
  styleUrls: ['./devices.component.css'],
  standalone:false
})
export class DevicesComponent implements OnInit {
  devices: UserDevice[] = [];
  filteredDevices: UserDevice[] = [];
  isLoading = false;
  searchTerm = '';
  sortColumn = 'lastUsedAt';
  sortDirection = 'desc';

  constructor(
    private authService: AuthService,
    private toastr: ToastrService
  ) { }

  ngOnInit(): void {
    this.loadDevices();
  }

  loadDevices(): void {
    this.isLoading = true;
    this.authService.getUserDevices().subscribe({
      next: (devices) => {
        this.devices = devices;
        this.filteredDevices = [...this.devices];
        this.sortDevicesWithCurrentFirst(); // Mevcut oturumu en üste al
        this.isLoading = false;
      },
      error: (error) => {
        this.toastr.error('Cihaz listesi alınamadı: ' + error.message);
        this.isLoading = false;
      }
    });
  }

  // Filter devices based on search term
  filterDevices(): void {
    if (!this.searchTerm.trim()) {
      this.filteredDevices = [...this.devices];
    } else {
      const term = this.searchTerm.toLowerCase().trim();
      this.filteredDevices = this.devices.filter(device =>
        device.deviceInfo.toLowerCase().includes(term) ||
        device.lastIpAddress.toLowerCase().includes(term)
      );
    }
    this.sortDevicesWithCurrentFirst(); // Mevcut oturumu en üstte tut
  }

  // Sort devices by column
  sortDevices(column: string): void {
    this.sortColumn = column;
    this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';

    this.filteredDevices.sort((a: any, b: any) => {
      let aValue = a[column];
      let bValue = b[column];

      // Handle date columns
      if (column === 'createdAt' || column === 'lastUsedAt') {
        aValue = new Date(aValue).getTime();
        bValue = new Date(bValue).getTime();
      }

      // Handle string columns
      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (aValue < bValue) {
        return this.sortDirection === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return this.sortDirection === 'asc' ? 1 : -1;
      }
      return 0;
    });
  }

  // Mevcut oturumu en üstte tutarak sıralama
  sortDevicesWithCurrentFirst(): void {
    this.filteredDevices.sort((a, b) => {
      // Önce mevcut oturum kontrolü
      if (a.isCurrentDevice && !b.isCurrentDevice) return -1;
      if (!a.isCurrentDevice && b.isCurrentDevice) return 1;

      // İkisi de mevcut oturum değilse veya ikisi de mevcut oturumsa, lastUsedAt'e göre sırala
      const dateA = new Date(a.lastUsedAt).getTime();
      const dateB = new Date(b.lastUsedAt).getTime();
      return dateB - dateA; // En son kullanılan en üstte
    });
  }

  // Get device icon based on device info
  getDeviceIcon(deviceInfo: string): string {
    const deviceInfo_lower = deviceInfo.toLowerCase();

    // Cihaz tipine göre ikon seç
    if (deviceInfo_lower.includes('iphone')) {
      return 'fab fa-apple';
    } else if (deviceInfo_lower.includes('ipad')) {
      return 'fas fa-tablet-alt';
    } else if (deviceInfo_lower.includes('android telefon')) {
      return 'fab fa-android';
    } else if (deviceInfo_lower.includes('android tablet')) {
      return 'fas fa-tablet-alt';
    } else if (deviceInfo_lower.includes('windows pc')) {
      return 'fab fa-windows';
    } else if (deviceInfo_lower.includes('mac')) {
      return 'fab fa-apple';
    } else if (deviceInfo_lower.includes('linux pc')) {
      return 'fab fa-linux';
    } else if (deviceInfo_lower.includes('chrome')) {
      return 'fab fa-chrome';
    } else if (deviceInfo_lower.includes('firefox')) {
      return 'fab fa-firefox';
    } else if (deviceInfo_lower.includes('edge')) {
      return 'fab fa-edge';
    } else if (deviceInfo_lower.includes('safari')) {
      return 'fab fa-safari';
    } else if (deviceInfo_lower.includes('opera')) {
      return 'fab fa-opera';
    } else if (deviceInfo_lower.includes('server')) {
      return 'fas fa-server';
    } else {
      return 'fas fa-desktop';
    }
  }

  // Get last active device name
  getLastActiveDevice(): string {
    if (this.devices.length === 0) return 'Yok';
    
    // Sort devices by last used date
    const sortedDevices = [...this.devices].sort((a, b) => {
      const dateA = new Date(a.lastUsedAt).getTime();
      const dateB = new Date(b.lastUsedAt).getTime();
      return dateB - dateA; // Descending order
    });
    
    // Return the name of the most recently used device
    return sortedDevices[0].deviceInfo.split(' ')[0];
  }

  // Get count of unique IP addresses
  getUniqueIpCount(): number {
    if (this.devices.length === 0) return 0;
    
    const uniqueIps = new Set(this.devices.map(device => device.lastIpAddress));
    return uniqueIps.size;
  }

  revokeDevice(deviceId: number): void {
    if (confirm('Bu cihazın oturumunu sonlandırmak istediğinize emin misiniz?')) {
      this.authService.revokeDevice(deviceId).subscribe({
        next: (response) => {
          if (response && response.success !== false) {
            this.toastr.success('Cihaz oturumu sonlandırıldı');
            this.loadDevices();
          } else {
            this.toastr.error('İşlem başarısız: ' + (response?.message || 'Bilinmeyen hata'));
          }
        },
        error: (error) => {
          const errorMessage = error?.message || 'Cihaz oturumu sonlandırılamadı';
          this.toastr.error('İşlem başarısız: ' + errorMessage);
        }
      });
    }
  }

  revokeAllDevices(): void {
    if (confirm('Tüm cihazların oturumunu sonlandırmak istediğinize emin misiniz? (Mevcut cihaz hariç)')) {
      this.authService.revokeAllDevices().subscribe({
        next: () => {
          this.toastr.success('Tüm cihaz oturumları sonlandırıldı');
          this.loadDevices();
        },
        error: (error) => {
          this.toastr.error('İşlem başarısız: ' + error.message);
        }
      });
    }
  }
}