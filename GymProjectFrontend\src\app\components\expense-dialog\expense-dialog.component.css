/* Modern Dialog Styles */
.modern-dialog {
  min-width: 320px;
  max-width: 100%;
  overflow: hidden;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  animation: zoomIn 0.3s ease-out;
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Header */
.modern-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  color: white;
  border-bottom: 1px solid var(--border-color);
}

.modern-card-header h2 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.modern-header-icon {
  font-size: 1.1rem;
}

.modern-btn-icon {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: var(--border-radius-sm);
  transition: background-color var(--transition-speed) ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.modern-btn-icon:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Body */
.modern-card-body {
  padding: var(--spacing-lg);
  max-height: 60vh;
  overflow-y: auto;
}

/* Form Styles */
.form-group {
  margin-bottom: var(--spacing-md);
}

.form-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.form-label-icon {
  color: var(--primary);
  font-size: 0.85rem;
}

.modern-form-control {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md);
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  font-size: 1rem;
  transition: all var(--transition-speed) ease;
}

.modern-form-control:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
}

.modern-form-control::placeholder {
  color: var(--text-secondary);
  opacity: 0.7;
}

/* Form Row */
.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-md);
}

.form-col {
  min-width: 0;
}

/* Input with Prefix */
.input-with-prefix {
  position: relative;
  display: flex;
  align-items: center;
}

.input-prefix {
  position: absolute;
  left: 0.75rem;
  color: var(--text-secondary);
  font-weight: 600;
  z-index: 1;
  pointer-events: none;
}

.modern-form-control.with-prefix {
  padding-left: 2rem;
}

/* Form Hint */
.form-hint {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.8rem;
  color: var(--text-secondary);
  margin-top: 0.25rem;
}

/* Form Error */
.form-error {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.8rem;
  color: var(--danger);
  margin-top: 0.25rem;
}

/* Footer */
.modern-card-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 0.75rem;
  padding: 1.25rem 1.5rem;
  border-top: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
}

/* Expense Dialog Action Buttons - Equal Size Design */
.expense-dialog-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 110px;
  height: 38px;
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius-md);
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  white-space: nowrap;
  user-select: none;
  gap: 0.5rem;
  border: 1px solid transparent;
  line-height: 1.5;
}

.expense-dialog-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

.expense-dialog-btn i {
  font-size: 0.875rem;
}

/* Cancel Button */
.expense-dialog-btn-cancel {
  background-color: transparent;
  color: var(--text-secondary);
  border-color: var(--border-color);
}

.expense-dialog-btn-cancel:hover:not(:disabled) {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
  border-color: var(--text-secondary);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Primary Button */
.expense-dialog-btn-primary {
  background-color: var(--primary);
  color: white;
  border-color: var(--primary);
}

.expense-dialog-btn-primary:hover:not(:disabled) {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(var(--primary-rgb), 0.3);
}

/* Dark Mode Support for Expense Dialog Buttons */
[data-theme="dark"] .expense-dialog-btn-cancel {
  background-color: transparent;
  color: var(--text-secondary);
  border-color: var(--border-color);
}

[data-theme="dark"] .expense-dialog-btn-cancel:hover:not(:disabled) {
  background-color: var(--bg-primary);
  color: var(--text-primary);
  border-color: var(--text-secondary);
}

[data-theme="dark"] .expense-dialog-btn-primary {
  background-color: var(--primary);
  color: white;
  border-color: var(--primary);
}

[data-theme="dark"] .expense-dialog-btn-primary:hover:not(:disabled) {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
}

[data-theme="dark"] .modern-card-footer {
  background-color: var(--bg-tertiary);
  border-top-color: var(--border-color);
}

/* Responsive Design */
@media (max-width: 768px) {
  .modern-dialog {
    min-width: 280px;
    margin: 1rem;
  }

  .modern-card-header {
    padding: var(--spacing-sm) var(--spacing-md);
  }

  .modern-card-header h2 {
    font-size: 1.1rem;
  }

  .modern-card-body {
    padding: var(--spacing-md);
  }

  .modern-card-footer {
    padding: 1rem;
    gap: 0.75rem;
    justify-content: center;
    flex-direction: row;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: var(--spacing-sm);
  }

  .expense-dialog-btn {
    min-width: 100px;
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
    height: 36px;
  }
}

@media (max-width: 480px) {
  .modern-dialog {
    min-width: 260px;
  }

  .modern-card-body {
    padding: var(--spacing-sm);
  }

  .form-group {
    margin-bottom: var(--spacing-sm);
  }

  .modern-card-footer {
    padding: 1rem 0.75rem;
    gap: 0.5rem;
    justify-content: center;
  }

  .expense-dialog-btn {
    min-width: 90px;
    padding: 0.5rem 0.8rem;
    font-size: 0.8rem;
    height: 34px;
  }
}

/* Dark Mode Support */
[data-theme="dark"] .modern-dialog {
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
}

[data-theme="dark"] .modern-card-header {
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  border-bottom-color: var(--border-color);
}

[data-theme="dark"] .modern-card-footer {
  background-color: var(--bg-tertiary);
  border-top-color: var(--border-color);
}

[data-theme="dark"] .modern-form-control {
  background-color: var(--bg-primary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

[data-theme="dark"] .modern-form-control:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.2);
}

